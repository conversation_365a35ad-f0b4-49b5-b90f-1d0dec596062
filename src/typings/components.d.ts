/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppProvider: typeof import('./../components/common/app-provider.vue')['default']
    BetterScroll: typeof import('./../components/custom/better-scroll.vue')['default']
    ButtonIcon: typeof import('./../components/custom/button-icon.vue')['default']
    CountTo: typeof import('./../components/custom/count-to.vue')['default']
    DarkModeContainer: typeof import('./../components/common/dark-mode-container.vue')['default']
    ExceptionBase: typeof import('./../components/common/exception-base.vue')['default']
    FullScreen: typeof import('./../components/common/full-screen.vue')['default']
    IconAntDesignEnterOutlined: typeof import('~icons/ant-design/enter-outlined')['default']
    IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined')['default']
    IconAntDesignSettingOutlined: typeof import('~icons/ant-design/setting-outlined')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    'IconIc:roundPlus': typeof import('~icons/ic/round-plus')['default']
    IconIcRoundDelete: typeof import('~icons/ic/round-delete')['default']
    IconIcRoundPlus: typeof import('~icons/ic/round-plus')['default']
    IconIcRoundRefresh: typeof import('~icons/ic/round-refresh')['default']
    IconIcRoundRemove: typeof import('~icons/ic/round-remove')['default']
    IconIcRoundSearch: typeof import('~icons/ic/round-search')['default']
    IconLocalBanner: typeof import('~icons/local/banner')['default']
    IconLocalLogo: typeof import('~icons/local/logo')['default']
    IconMdiArrowDownThin: typeof import('~icons/mdi/arrow-down-thin')['default']
    IconMdiArrowUpThin: typeof import('~icons/mdi/arrow-up-thin')['default']
    IconMdiDrag: typeof import('~icons/mdi/drag')['default']
    IconMdiKeyboardEsc: typeof import('~icons/mdi/keyboard-esc')['default']
    IconMdiKeyboardReturn: typeof import('~icons/mdi/keyboard-return')['default']
    IconMdiRefresh: typeof import('~icons/mdi/refresh')['default']
    IconUilSearch: typeof import('~icons/uil/search')['default']
    LangSwitch: typeof import('./../components/common/lang-switch.vue')['default']
    LookForward: typeof import('./../components/custom/look-forward.vue')['default']
    MenuToggler: typeof import('./../components/common/menu-toggler.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDescriptions: typeof import('naive-ui')['NDescriptions']
    NDescriptionsItem: typeof import('naive-ui')['NDescriptionsItem']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicInput: typeof import('naive-ui')['NDynamicInput']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSpace: typeof import('naive-ui')['NSpace']
    NStatistic: typeof import('naive-ui')['NStatistic']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NThing: typeof import('naive-ui')['NThing']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NWatermark: typeof import('naive-ui')['NWatermark']
    PinToggler: typeof import('./../components/common/pin-toggler.vue')['default']
    ReloadButton: typeof import('./../components/common/reload-button.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SoybeanAvatar: typeof import('./../components/custom/soybean-avatar.vue')['default']
    SvgIcon: typeof import('./../components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../components/advanced/table-header-operation.vue')['default']
    ThemeSchemaSwitch: typeof import('./../components/common/theme-schema-switch.vue')['default']
    WaveBg: typeof import('./../components/custom/wave-bg.vue')['default']
  }
}
